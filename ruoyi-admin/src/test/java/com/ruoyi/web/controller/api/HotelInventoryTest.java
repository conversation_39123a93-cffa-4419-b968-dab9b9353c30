package com.ruoyi.web.controller.api;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.system.service.IHotelOrderService;
import com.ruoyi.system.service.ICategoryCodeRoomService;
import com.ruoyi.system.domain.HotelOrder;
import com.ruoyi.system.domain.CategoryCodeRoom;

import java.util.Date;
import java.util.Calendar;
import java.math.BigDecimal;

/**
 * 酒店房间库存校验测试
 * 
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class HotelInventoryTest {

    @Autowired
    private IHotelOrderService hotelOrderService;

    @Autowired
    private ICategoryCodeRoomService categoryCodeRoomService;

    /**
     * 测试房间库存统计功能
     */
    @Test
    public void testRoomInventory() {
        Long roomId = 1L;
        Long conferenceId = 1L;
        
        // 获取房间总库存
        int totalInventory = categoryCodeRoomService.getRoomInventory(roomId, conferenceId);
        System.out.println("房间ID " + roomId + " 的总库存: " + totalInventory);
        
        // 测试日期范围
        Calendar cal = Calendar.getInstance();
        Date checkinDate = cal.getTime();
        cal.add(Calendar.DAY_OF_MONTH, 2);
        Date checkoutDate = cal.getTime();
        
        // 统计已预订数量
        int bookedCount = hotelOrderService.countBookedRooms(roomId, conferenceId, checkinDate, checkoutDate);
        System.out.println("已预订数量: " + bookedCount);
        
        int availableCount = totalInventory - bookedCount;
        System.out.println("剩余可用数量: " + availableCount);
        
        // 验证结果
        assert totalInventory >= 0 : "总库存不能为负数";
        assert bookedCount >= 0 : "已预订数量不能为负数";
        assert availableCount >= 0 : "剩余数量不能为负数";
    }

    /**
     * 测试创建订单对库存的影响
     */
    @Test
    public void testOrderCreationImpactOnInventory() {
        Long roomId = 1L;
        Long conferenceId = 1L;
        
        // 测试日期范围
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 10); // 使用未来日期避免与现有订单冲突
        Date checkinDate = cal.getTime();
        cal.add(Calendar.DAY_OF_MONTH, 2);
        Date checkoutDate = cal.getTime();
        
        // 获取创建订单前的已预订数量
        int bookedCountBefore = hotelOrderService.countBookedRooms(roomId, conferenceId, checkinDate, checkoutDate);
        System.out.println("创建订单前已预订数量: " + bookedCountBefore);
        
        // 创建测试订单
        HotelOrder testOrder = new HotelOrder();
        testOrder.setOrderNo("TEST" + System.currentTimeMillis());
        testOrder.setUserId(1L);
        testOrder.setOpenid("test_openid");
        testOrder.setConferenceId(conferenceId);
        testOrder.setRoomId(roomId);
        testOrder.setRoomType("TEST");
        testOrder.setRoomName("测试房间");
        testOrder.setCheckinDate(checkinDate);
        testOrder.setCheckoutDate(checkoutDate);
        testOrder.setNights(2);
        testOrder.setRoomPrice(new BigDecimal("200.00"));
        testOrder.setTotalAmount(new BigDecimal("400.00"));
        testOrder.setDepositAmount(new BigDecimal("100.00"));
        testOrder.setOrderStatus(HotelOrder.OrderStatus.PENDING);
        testOrder.setPaymentStatus(HotelOrder.PaymentStatus.UNPAID);
        testOrder.setGuestName("测试用户");
        testOrder.setGuestPhone("13800138000");
        testOrder.setCreateBy("test");
        
        // 插入订单
        int result = hotelOrderService.insertHotelOrder(testOrder);
        assert result > 0 : "订单创建失败";
        
        // 获取创建订单后的已预订数量
        int bookedCountAfter = hotelOrderService.countBookedRooms(roomId, conferenceId, checkinDate, checkoutDate);
        System.out.println("创建订单后已预订数量: " + bookedCountAfter);
        
        // 验证已预订数量增加了1
        assert bookedCountAfter == bookedCountBefore + 1 : "创建订单后已预订数量应该增加1";
        
        System.out.println("库存校验测试通过！");
    }

    /**
     * 测试取消订单对库存的影响
     */
    @Test
    public void testOrderCancellationImpactOnInventory() {
        Long roomId = 1L;
        Long conferenceId = 1L;
        
        // 测试日期范围
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 15); // 使用未来日期
        Date checkinDate = cal.getTime();
        cal.add(Calendar.DAY_OF_MONTH, 2);
        Date checkoutDate = cal.getTime();
        
        // 创建测试订单
        HotelOrder testOrder = new HotelOrder();
        testOrder.setOrderNo("CANCEL_TEST" + System.currentTimeMillis());
        testOrder.setUserId(1L);
        testOrder.setOpenid("test_openid");
        testOrder.setConferenceId(conferenceId);
        testOrder.setRoomId(roomId);
        testOrder.setRoomType("TEST");
        testOrder.setRoomName("测试房间");
        testOrder.setCheckinDate(checkinDate);
        testOrder.setCheckoutDate(checkoutDate);
        testOrder.setNights(2);
        testOrder.setRoomPrice(new BigDecimal("200.00"));
        testOrder.setTotalAmount(new BigDecimal("400.00"));
        testOrder.setDepositAmount(new BigDecimal("100.00"));
        testOrder.setOrderStatus(HotelOrder.OrderStatus.PENDING);
        testOrder.setPaymentStatus(HotelOrder.PaymentStatus.UNPAID);
        testOrder.setGuestName("测试用户");
        testOrder.setGuestPhone("13800138000");
        testOrder.setCreateBy("test");
        
        // 插入订单
        hotelOrderService.insertHotelOrder(testOrder);
        
        // 获取取消前的已预订数量
        int bookedCountBefore = hotelOrderService.countBookedRooms(roomId, conferenceId, checkinDate, checkoutDate);
        System.out.println("取消前已预订数量: " + bookedCountBefore);
        
        // 取消订单
        hotelOrderService.cancelOrder(testOrder.getOrderNo(), "测试取消", "test");
        
        // 获取取消后的已预订数量
        int bookedCountAfter = hotelOrderService.countBookedRooms(roomId, conferenceId, checkinDate, checkoutDate);
        System.out.println("取消后已预订数量: " + bookedCountAfter);
        
        // 验证已预订数量减少了1
        assert bookedCountAfter == bookedCountBefore - 1 : "取消订单后已预订数量应该减少1";
        
        System.out.println("订单取消库存校验测试通过！");
    }
}
