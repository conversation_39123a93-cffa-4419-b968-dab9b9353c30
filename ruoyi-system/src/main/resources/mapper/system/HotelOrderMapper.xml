<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HotelOrderMapper">
    
    <resultMap type="HotelOrder" id="HotelOrderResult">
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="userId"    column="user_id"    />
        <result property="openid"    column="openid"    />
        <result property="conferenceId"    column="conference_id"    />
        <result property="roomId"    column="room_id"    />
        <result property="roomType"    column="room_type"    />
        <result property="roomName"    column="room_name"    />
        <result property="checkinDate"    column="checkin_date"    />
        <result property="checkoutDate"    column="checkout_date"    />
        <result property="nights"    column="nights"    />
        <result property="roomPrice"    column="room_price"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="depositAmount"    column="deposit_amount"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="paymentStatus"    column="payment_status"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="prepayId"    column="prepay_id"    />
        <result property="paymentTime"    column="payment_time"    />
        <result property="confirmTime"    column="confirm_time"    />
        <result property="cancelTime"    column="cancel_time"    />
        <result property="cancelReason"    column="cancel_reason"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="refundTime"    column="refund_time"    />
        <result property="refundReason"    column="refund_reason"    />
        <result property="guestName"    column="guest_name"    />
        <result property="guestPhone"    column="guest_phone"    />
        <result property="guestIdCard"    column="guest_id_card"    />
        <result property="conferenceTitle"    column="conference_title"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHotelOrderVo">
        select ho.order_id, ho.order_no, ho.user_id, ho.openid, ho.conference_id, ho.room_id, ho.room_type, ho.room_name, ho.checkin_date, ho.checkout_date, ho.nights, ho.room_price, ho.total_amount, ho.deposit_amount, ho.order_status, ho.payment_status, ho.payment_method, ho.transaction_id, ho.prepay_id, ho.payment_time, ho.confirm_time, ho.cancel_time, ho.cancel_reason, ho.refund_amount, ho.refund_time, ho.refund_reason, ho.guest_name, ho.guest_phone, ho.guest_id_card, ho.remark, ho.create_by, ho.create_time, ho.update_by, ho.update_time from hotel_order ho
    </sql>

    <select id="selectHotelOrderList" parameterType="HotelOrder" resultMap="HotelOrderResult">
        select ho.order_id, ho.order_no, ho.user_id, ho.openid, ho.conference_id, ho.room_id, ho.room_type, ho.room_name, ho.checkin_date, ho.checkout_date, ho.nights, ho.room_price, ho.total_amount, ho.deposit_amount, ho.order_status, ho.payment_status, ho.payment_method, ho.transaction_id, ho.prepay_id, ho.payment_time, ho.confirm_time, ho.cancel_time, ho.cancel_reason, ho.refund_amount, ho.refund_time, ho.refund_reason, ho.guest_name, ho.guest_phone, ho.guest_id_card, ho.remark, ho.create_by, ho.create_time, ho.update_by, ho.update_time,
               c.conference_title as conference_title
        from hotel_order ho
        left join conference c on ho.conference_id = c.id
        <where>
            <if test="orderNo != null  and orderNo != ''"> and ho.order_no = #{orderNo}</if>
            <if test="userId != null "> and ho.user_id = #{userId}</if>
            <if test="openid != null  and openid != ''"> and ho.openid = #{openid}</if>
            <if test="conferenceId != null "> and ho.conference_id = #{conferenceId}</if>
            <if test="roomId != null "> and ho.room_id = #{roomId}</if>
            <if test="roomType != null  and roomType != ''"> and ho.room_type = #{roomType}</if>
            <if test="roomName != null  and roomName != ''"> and ho.room_name like concat('%', #{roomName}, '%')</if>
            <if test="checkinDate != null "> and ho.checkin_date = #{checkinDate}</if>
            <if test="checkoutDate != null "> and ho.checkout_date = #{checkoutDate}</if>
            <if test="orderStatus != null  and orderStatus != ''"> and ho.order_status = #{orderStatus}</if>
            <if test="paymentStatus != null  and paymentStatus != ''"> and ho.payment_status = #{paymentStatus}</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and ho.payment_method = #{paymentMethod}</if>
            <if test="transactionId != null  and transactionId != ''"> and ho.transaction_id = #{transactionId}</if>
            <if test="guestName != null  and guestName != ''"> and ho.guest_name like concat('%', #{guestName}, '%')</if>
            <if test="guestPhone != null  and guestPhone != ''"> and ho.guest_phone = #{guestPhone}</if>
        </where>
        order by ho.create_time desc
    </select>
    
    <select id="selectHotelOrderByOrderId" parameterType="Long" resultMap="HotelOrderResult">
        <include refid="selectHotelOrderVo"/>
        where order_id = #{orderId}
    </select>

    <select id="selectHotelOrderByOrderNo" parameterType="String" resultMap="HotelOrderResult">
        <include refid="selectHotelOrderVo"/>
        where order_no = #{orderNo}
    </select>

    <select id="selectHotelOrderByTransactionId" parameterType="String" resultMap="HotelOrderResult">
        <include refid="selectHotelOrderVo"/>
        where transaction_id = #{transactionId}
    </select>

    <select id="selectHotelOrderListByUserId" parameterType="Long" resultMap="HotelOrderResult">
        <include refid="selectHotelOrderVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>

    <select id="selectHotelOrderListByOpenid" parameterType="String" resultMap="HotelOrderResult">
        <include refid="selectHotelOrderVo"/>
        where openid = #{openid}
        order by create_time desc
    </select>

    <select id="selectHotelOrderListByConferenceId" parameterType="Long" resultMap="HotelOrderResult">
        <include refid="selectHotelOrderVo"/>
        where conference_id = #{conferenceId}
        order by create_time desc
    </select>

    <select id="selectTimeoutPendingOrders" resultMap="HotelOrderResult">
        <include refid="selectHotelOrderVo"/>
        where order_status = 'PENDING' 
        and payment_status = 'UNPAID'
        and create_time &lt; DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
        order by create_time asc
    </select>

    <select id="countHotelOrders" parameterType="HotelOrder" resultType="int">
        select count(*) from hotel_order
        <where>  
            <if test="orderStatus != null  and orderStatus != ''"> and order_status = #{orderStatus}</if>
            <if test="paymentStatus != null  and paymentStatus != ''"> and payment_status = #{paymentStatus}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="conferenceId != null "> and conference_id = #{conferenceId}</if>
        </where>
    </select>

    <select id="countUserOrders" resultType="int">
        select count(*) from hotel_order
        where user_id = #{userId}
        <if test="orderStatus != null and orderStatus != ''">
            and order_status = #{orderStatus}
        </if>
    </select>
        
    <insert id="insertHotelOrder" parameterType="HotelOrder" useGeneratedKeys="true" keyProperty="orderId">
        insert into hotel_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="openid != null">openid,</if>
            <if test="conferenceId != null">conference_id,</if>
            <if test="roomId != null">room_id,</if>
            <if test="roomType != null">room_type,</if>
            <if test="roomName != null">room_name,</if>
            <if test="checkinDate != null">checkin_date,</if>
            <if test="checkoutDate != null">checkout_date,</if>
            <if test="nights != null">nights,</if>
            <if test="roomPrice != null">room_price,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="depositAmount != null">deposit_amount,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="paymentStatus != null">payment_status,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="prepayId != null">prepay_id,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="confirmTime != null">confirm_time,</if>
            <if test="cancelTime != null">cancel_time,</if>
            <if test="cancelReason != null">cancel_reason,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="refundTime != null">refund_time,</if>
            <if test="refundReason != null">refund_reason,</if>
            <if test="guestName != null">guest_name,</if>
            <if test="guestPhone != null">guest_phone,</if>
            <if test="guestIdCard != null">guest_id_card,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="openid != null">#{openid},</if>
            <if test="conferenceId != null">#{conferenceId},</if>
            <if test="roomId != null">#{roomId},</if>
            <if test="roomType != null">#{roomType},</if>
            <if test="roomName != null">#{roomName},</if>
            <if test="checkinDate != null">#{checkinDate},</if>
            <if test="checkoutDate != null">#{checkoutDate},</if>
            <if test="nights != null">#{nights},</if>
            <if test="roomPrice != null">#{roomPrice},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="depositAmount != null">#{depositAmount},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="paymentStatus != null">#{paymentStatus},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="prepayId != null">#{prepayId},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
            <if test="cancelTime != null">#{cancelTime},</if>
            <if test="cancelReason != null">#{cancelReason},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="refundTime != null">#{refundTime},</if>
            <if test="refundReason != null">#{refundReason},</if>
            <if test="guestName != null">#{guestName},</if>
            <if test="guestPhone != null">#{guestPhone},</if>
            <if test="guestIdCard != null">#{guestIdCard},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateHotelOrder" parameterType="HotelOrder">
        update hotel_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="conferenceId != null">conference_id = #{conferenceId},</if>
            <if test="roomId != null">room_id = #{roomId},</if>
            <if test="roomType != null">room_type = #{roomType},</if>
            <if test="roomName != null">room_name = #{roomName},</if>
            <if test="checkinDate != null">checkin_date = #{checkinDate},</if>
            <if test="checkoutDate != null">checkout_date = #{checkoutDate},</if>
            <if test="nights != null">nights = #{nights},</if>
            <if test="roomPrice != null">room_price = #{roomPrice},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="depositAmount != null">deposit_amount = #{depositAmount},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="paymentStatus != null">payment_status = #{paymentStatus},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="prepayId != null">prepay_id = #{prepayId},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="cancelReason != null">cancel_reason = #{cancelReason},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundTime != null">refund_time = #{refundTime},</if>
            <if test="refundReason != null">refund_reason = #{refundReason},</if>
            <if test="guestName != null">guest_name = #{guestName},</if>
            <if test="guestPhone != null">guest_phone = #{guestPhone},</if>
            <if test="guestIdCard != null">guest_id_card = #{guestIdCard},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where order_id = #{orderId}
    </update>

    <update id="updateOrderPaymentInfo">
        update hotel_order set 
            transaction_id = #{transactionId},
            payment_time = #{paymentTime},
            order_status = #{orderStatus},
            payment_status = #{paymentStatus},
            payment_method = #{paymentMethod},
            update_time = sysdate()
        where order_no = #{orderNo}
    </update>

    <update id="updateOrderStatus">
        update hotel_order set 
            order_status = #{orderStatus},
            update_by = #{updateBy},
            update_time = sysdate()
        where order_no = #{orderNo}
    </update>

    <update id="updatePaymentStatus">
        update hotel_order set 
            payment_status = #{paymentStatus},
            update_by = #{updateBy},
            update_time = sysdate()
        where order_no = #{orderNo}
    </update>

    <update id="cancelOrder">
        update hotel_order set
            order_status = 'CANCELLED',
            cancel_time = sysdate(),
            cancel_reason = #{cancelReason},
            update_by = #{updateBy},
            update_time = sysdate()
        where order_no = #{orderNo}
    </update>

    <update id="confirmOrder">
        update hotel_order set
            order_status = 'CONFIRMED',
            confirm_time = sysdate(),
            update_by = #{updateBy},
            update_time = sysdate()
        where order_no = #{orderNo}
    </update>

    <update id="refundOrder">
        update hotel_order set
            order_status = 'REFUNDED',
            payment_status = 'REFUNDED',
            refund_amount = #{refundAmount},
            refund_time = sysdate(),
            refund_reason = #{refundReason},
            update_by = #{updateBy},
            update_time = sysdate()
        where order_no = #{orderNo}
    </update>

    <update id="updateTransactionId">
        update hotel_order set
            transaction_id = #{transactionId},
            update_by = #{updateBy},
            update_time = sysdate()
        where order_no = #{orderNo}
    </update>

    <delete id="deleteHotelOrderByOrderId" parameterType="Long">
        delete from hotel_order where order_id = #{orderId}
    </delete>

    <delete id="deleteHotelOrderByOrderIds" parameterType="String">
        delete from hotel_order where order_id in
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

    <!-- 统计指定房间在指定日期范围内的已预订数量 -->
    <select id="countBookedRooms" resultType="int">
        SELECT COUNT(*)
        FROM hotel_order
        WHERE room_id = #{roomId}
          AND conference_id = #{conferenceId}
          AND order_status != 'CANCELLED'
          AND (
              (checkin_date &lt;= #{checkinDate} AND checkout_date &gt; #{checkinDate})
              OR (checkin_date &lt; #{checkoutDate} AND checkout_date &gt;= #{checkoutDate})
              OR (checkin_date &gt;= #{checkinDate} AND checkout_date &lt;= #{checkoutDate})
          )
    </select>
</mapper>
